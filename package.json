{"name": "bora-estudar-front", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.11", "@angular/cdk": "^17.3.10", "@angular/common": "^17.3.11", "@angular/compiler": "^17.3.11", "@angular/core": "^17.3.11", "@angular/forms": "^17.3.11", "@angular/material": "^17.3.10", "@angular/platform-browser": "^17.3.11", "@angular/platform-browser-dynamic": "^17.3.11", "@angular/router": "^17.3.11", "primeng": "^17.18.7", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone": "^0.3.4", "zone.js": "~0.14.7"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.8", "@angular/cli": "^17.3.8", "@angular/compiler-cli": "^17.3.11", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "schematics-scss-migrate": "^2.3.17", "typescript": "^5.4.5"}}