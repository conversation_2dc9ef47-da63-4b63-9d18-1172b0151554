import {
  ActivatedRouteSnapshot,
  CanActivateFn,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { inject } from '@angular/core';
import { AuthService } from '../auth/auth.service';
import { Observable } from 'rxjs/internal/Observable';
import { map } from 'rxjs/internal/operators/map';
import { tap } from 'rxjs/internal/operators/tap';

/**
 * A guard that prevents access to a route if the user is already logged in.
 * If the user is logged in, it redirects to the '/search' route.
 * If the user is not logged in, it allows access to the route.
 * @param _route - The activated route snapshot.
 * @param _state - The router state snapshot.
 * @returns A boolean or UrlTree indicating whether to allow or deny access to the route.
 */
export const loggedInGuard: CanActivateFn = (
  _route: ActivatedRouteSnapshot,
  _state: RouterStateSnapshot
): Observable<boolean | UrlTree> => {
  console.log('The loggedInGuard is being called correctly');
  const authService = inject(AuthService);
  const router = inject(Router);

  return authService.isLoggedIn().pipe(
    map((isLoggedIn) => {
      if (isLoggedIn) {
        return router.createUrlTree(['/search']);
        // router.navigateByUrl('/search');
        // return false;
      }
      return true;
    })
  );
};

/**
 * A guard that checks if the user is authenticated before allowing access to a route.
 * If the user is not authenticated, it redirects to the login page.
 *
 * @param _route - The activated route snapshot.
 * @param _state - The router state snapshot.
 * @returns A boolean indicating whether the user is authenticated or not, or a UrlTree to redirect to the login page.
 */
export const authGuard: CanActivateFn = (
  _route: ActivatedRouteSnapshot,
  _state: RouterStateSnapshot
): Observable<boolean | UrlTree> => {
  console.log('The authGuard is being called correctly');
  const authService = inject(AuthService);
  const router = inject(Router);
  const signed = localStorage.getItem('signed-user');

  return authService.isLoggedIn().pipe(
    map((isLoggedIn) => {
      if (!isLoggedIn) {
        return router.createUrlTree(['/login']);
        // router.navigateByUrl('/login');
        // return false;
      }
      if (!signed) {
        console.error('Usuário não encontrado no localStorage');
        return router.createUrlTree(['/associate']);
      }
      return true;
    })
  );
};

/**
 * A guard that prevents access to a route if the user is already logged in.
 * If the user is logged in, it redirects to the '/search' route.
 * If the user is not logged in, it allows access to the route.
 * @param _route - The activated route snapshot.
 * @param _state - The router state snapshot.
 * @returns A boolean or UrlTree indicating whether to allow or deny access to the route.
 */
export const discordAssociateGuard: CanActivateFn = (
  _route: ActivatedRouteSnapshot,
  _state: RouterStateSnapshot
): Observable<boolean | UrlTree> => {
  console.log('The discordAssociateGuard is being called correctly');
  const authService = inject(AuthService);
  const router = inject(Router);

  const signed = localStorage.getItem('signed-user');

  return authService.isLoggedIn().pipe(
    map((isLoggedIn) => {
      if (!isLoggedIn) {
        console.log('The discordAssociateGuard 2');
        return router.createUrlTree(['/login']);
      }
      if (!signed) {
        return true;
      }
      return router.createUrlTree(['/search']);
    })
  );
};
