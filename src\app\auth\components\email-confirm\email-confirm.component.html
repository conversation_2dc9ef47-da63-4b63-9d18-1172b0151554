<div class="container">
  <mat-card>
    <mat-card-title>
      <div class="logo">
        <img src="assets/logo.svg" alt="Logo" />
      </div>
    </mat-card-title>

    <mat-card-content>
      <div class="feedback-message">
        <p>{{ message }}</p>
      </div>

      <button
        mat-raised-button
        color="primary"
        class="form-button"
        routerLink="/login"
        *ngIf="message.toLowerCase().includes('sucesso')"
      >
        Ir para o login
      </button>
    </mat-card-content>
  </mat-card>
</div>
