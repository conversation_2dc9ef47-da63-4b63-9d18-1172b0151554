.search_container {
  display: flex;
  flex-wrap: wrap;
  padding: 16px;
}

.search_row {
  display: flex;
  height: 56px;
  width: 100%;
  gap: 1%;
}

.search_row_2 {
  display: flex;
  height: 56px;
  width: 100%;
  gap: 1%;
}

.search_row_search_bar {
  height: 100%;
  width: 70%;
}

.search_row_search_item {
  height: 100%;
  width: 15%;
}

.search_row_icon {
  margin: 0px !important;
}

.position {
  justify-content: center;
  display: flex;
  margin-top: 15px;
  padding: 0 5px;
}

::ng-deep .cdk-overlay-pane {
  .mat-mdc-menu-panel {
    max-width: fit-content;
  }
}

.pesquisarButton {
  margin: 10px;
  color: white !important;
  background: #3f51b5;
  width: 200px;
  height: 50px;
}

.fabButton {
  left: 80%;
  border-radius: 14px;
  position: fixed;
  top: 80%;
  z-index: 10;
  background: #3f51b5;
  color: white !important;
  height: 50px;
  border: none;
  padding-left: 14px !important;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.75);
    -webkit-box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.75);
}

.input6 {
  width: 6rem;
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  padding: 16px;
  gap: 0;
}

@media (min-width: 768px) {

  .search_row_2 {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: stretch;
    height: 56px;
    width: 100%;
    gap: 1%;
  }
}

.checkbox-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

@media (max-width: 767px) {
  .checkbox-wrapper {
    flex-direction: column;
    align-items: flex-start;
    padding: 0 12px;
  }
}
