<div class="search_container">
  <div class="search_row">
    <mat-form-field appearance="outline" class="search_row_search_bar">
      <mat-label>Disciplina</mat-label>
      <input #input matInput class="search_row_search_bar" [matAutocomplete]="auto" (input)="filter()" (focus)="filter()" />
      <mat-autocomplete requireSelection #auto="matAutocomplete">
        <mat-option *ngFor="let option of filteredOptions" [value]="option.code + ' - ' + option.title">
          ({{ option.code }}) {{ option.title }}
        </mat-option>
      </mat-autocomplete>
    </mat-form-field>

    <button
      mat-raised-button
      color="primary"
      class="search_row_search_item"
      (click)="applyFilters()">
      <mat-icon class="search_row_icon">search</mat-icon>
    </button>

    <button
    mat-raised-button
    color="primary"
    class="search_row_search_item"
    (click)="clearFilters()">Limpar Filtro
    </button>
  </div>

  <div class="search_row_2">
    <button mat-button [matMenuTriggerFor]="menu" class="pesquisarButton"><PERSON><PERSON></button>
    <mat-menu #menu="matMenu" style="max-width: auto !important;">
      <div class="position">
        <section style="display: flex;">
          <mat-checkbox color="primary" (click)="$event.stopPropagation(); days('dom')">DOM</mat-checkbox>
          <mat-checkbox color="primary" (click)="$event.stopPropagation(); days('seg')">SEG</mat-checkbox>
          <mat-checkbox color="primary" (click)="$event.stopPropagation(); days('ter')">TER</mat-checkbox>
          <mat-checkbox color="primary" (click)="$event.stopPropagation(); days('qua')">QUA</mat-checkbox>
          <mat-checkbox color="primary" (click)="$event.stopPropagation(); days('qui')">QUI</mat-checkbox>
          <mat-checkbox color="primary" (click)="$event.stopPropagation(); days('sex')">SEX</mat-checkbox>
          <mat-checkbox color="primary" (click)="$event.stopPropagation(); days('sab')">SAB</mat-checkbox>
        </section>
      </div>
    </mat-menu>

    <button mat-button [matMenuTriggerFor]="menuHora" class="pesquisarButton">Hora de Início</button>
    <mat-menu #menuHora="matMenu" class="position">
      <div class="position">
        <mat-form-field appearance="outline" class="input6" (click)="$event.stopPropagation()">
          <mat-label>A partir de:</mat-label>
          <input #time matInput type="time" (change)="onHourChange($event)"/>
        </mat-form-field>
      </div>
    </mat-menu>
  </div>

  <div class="container">
    <div *ngFor="let groups of options">
      <app-study-group-search-item [studyGroup]="groups"></app-study-group-search-item>
    </div>
  </div>
</div>
