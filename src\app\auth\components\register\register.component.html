<div class="container">
  <mat-card>
    <mat-card-title> <PERSON><PERSON>uda<PERSON> - UFF! </mat-card-title>

    <mat-card-content>
      <form [formGroup]="signupForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline">
          <mat-label>Nome</mat-label>
          <input matInput formControlName="name" type="text" required />
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Email</mat-label>
          <input matInput formControlName="email" type="email" required />
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Senha</mat-label>
          <input matInput formControlName="password" type="password" required />
        </mat-form-field>

        <button
          mat-raised-button
          color="primary"
          class="form-button"
          type="submit"
        >
          Cadastrar
        </button>

        <a
          mat-stroked-button
          color="link"
          class="form-button"
          routerLink="/login"
          >Voltar
        </a>
      </form>
    </mat-card-content>
  </mat-card>
</div>
