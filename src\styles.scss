/* You can add global styles to this file, and also import other style files */
@import "primeng/resources/themes/lara-light-blue/theme.css";
@import "primeng/resources/primeng.css";

html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: Roboto, 'Helvetica Neue', sans-serif;
}

.spacer {
  flex: 1 1 auto;
}

.allGrid{
  display: grid;
  justify-content: center;
}

.allFlex{
  display: flex;
  justify-content: center;
}

.allBetween{
  display: flex;
  justify-content: space-between;
}

.m-top{
  margin-top: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
  color: white
}
