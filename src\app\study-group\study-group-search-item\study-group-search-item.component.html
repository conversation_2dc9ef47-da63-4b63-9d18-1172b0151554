<div class="container">
  <mat-card>
    <mat-card-header>
      <mat-card-title class="study_group_item_title limited-title" matTooltip="{{ studyGroup.title }}">{{ studyGroup.title }}</mat-card-title>
      <mat-card-subtitle class="limited-subtitle" matTooltip="{{ studyGroup.shortDescription }}">{{ studyGroup.shortDescription }}</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <!-- <p>
        <strong>Monitor:</strong>
        {{ studyGroup.monitor }}
      </p> -->
      <p>
        <strong>Alunos:</strong>
        {{ studyGroup.participants }}
      </p>
      <p>
        <strong>Modalidade:</strong>
        {{ studyGroup.modality | titlecase }}
      </p>
      <p>
        <strong>Hora de Início:</strong>
        {{ studyGroup.hour }}
      </p>
      <mat-chip-set>
        <mat-chip class="selected" *ngFor="let day of studyGroup.daysOfWeek">
         <p class="selectedText">{{day | titlecase}}</p>
        </mat-chip>
      </mat-chip-set>
    </mat-card-content>

    <mat-card-actions>
      <button
        mat-raised-button
        color="primary"
        class="full_width"
        (click)="openDetalheDialog(studyGroup)">
        Detalhes
      </button>
    </mat-card-actions>
  </mat-card>
</div>

<!-- [routerLink]="['/study-group', studyGroup.id]" -->
