<div class="filter-container">
  <form [formGroup]="formulario" >
    <div class="row">
      <mat-form-field appearance="outline" class="search_row_search_bar">
        <mat-label>Disciplina</mat-label>
        <input #input matInput formControlName="campoOculto" class="search_row_search_bar" [matAutocomplete]="auto" (input)="filter()" (focus)="filter()" />
        <mat-autocomplete requireSelection #auto="matAutocomplete" (optionSelected)="onOptionSelected($event.option.value)">
          <mat-option *ngFor="let option of filteredOptions" [value]="option">
          <!-- <mat-option *ngFor="let option of filteredOptions" [value]="option.code + ' - ' + option.name"> -->
            ({{ option.code }}) {{ option.name }}
          </mat-option>
        </mat-autocomplete>
      </mat-form-field>
    </div>

    <mat-form-field class="input100">
      <mat-label>Descrição</mat-label>
      <textarea matInput formControlName="description" maxlength="255"></textarea>
      <mat-hint align="end">{{ formulario.get('description')?.value?.length || 0 }} / 255</mat-hint>
    </mat-form-field>

    <div class="row">
      <mat-form-field>
        <mat-label>Número de Alunos</mat-label>
        <mat-select formControlName="maxStudents">
          <mat-option [value]="2">2</mat-option>
          <mat-option [value]="3">3</mat-option>
          <mat-option [value]="4">4</mat-option>
          <mat-option [value]="5">5</mat-option>
          <mat-option [value]="6">6</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field class="margin input30">
        <mat-label>Hora de Início</mat-label>
        <input matInput type="time" formControlName="meetingTime"/>
      </mat-form-field>
    </div>

    <h2>Dias da Semana</h2>
    <mat-chip-listbox formControlName="weekdays" multiple>
      <mat-chip-option *ngFor="let day of daysOfWeek" [value]="day">
        {{ day.name }}
      </mat-chip-option>
    </mat-chip-listbox>

    <div class="allCenter">
      <p-toast />

      @if(loading === true){
        <p-progressSpinner ariaLabel="loading" />
      } @else {
        <button mat-raised-button color="primary" class="input100" (click)="editGroups()">Atualizar Grupo</button>
      }
    </div>
  </form>
</div>
