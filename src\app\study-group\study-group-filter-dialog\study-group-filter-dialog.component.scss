/* .full-screen-dialog .mat-dialog-container {
  height: 100%;
  width: 100%;
  padding: 0;
} */

/* .filter-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
  mat-form-field,
  mat-selection-list {
    width: 100%;
    margin-bottom: 16px;
  }
  button {
    align-self: flex-end;
  }
} */

/* mat-form-field,
mat-selection-list {
  width: 100%;
  margin-bottom: 16px;
} */


.filter-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.spacer {
  flex: 1 1 auto;
}


/* button {
  align-self: flex-end;
} */



