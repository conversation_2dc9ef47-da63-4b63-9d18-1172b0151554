.header_container {
  margin-top: 65px;
display: flex;
flex-direction: column;
position: fixed;
top: 0;
bottom: 0;
left: 0;
right: 0;

}

.header_toolbar {
  position: fixed;
  /* Make sure the toolbar will stay on top of the content as it scrolls past. */
  /* z-index: 2; */
}

.spacer {
  flex: 1 1 auto;
}

h1.app_name {
  margin-left: 8px;
}

.sidenav_container {
  margin-top: 65px;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  /* z-index: 2; */
}

.mat_sidenav_content {
  /* When the sidenav is not fixed, stretch the sidenav container to fill the available space. This
     causes `<mat-sidenav-content>` to act as our scrolling element for desktop layouts. */
     /* z-index: 2; */
}

.header_container .sidenav_container {
  /* When the sidenav is fixed, don't constrain the height of the sidenav container. This allows the
     `<body>` to be our scrolling element for mobile layouts. */
  flex: 1 0 auto;
}

.example-spacer {
  flex: 1 1 auto;
}

/* .example-container {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;
} */
