.container {
  width: 100%;
  height: 100vh;
  background-color: #fafafa;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.logo {
  max-width: 100%;
  padding: 18px;

  img {
    width: 100%;
    -webkit-box-shadow: 0px 0px 5px 1px rgba(0, 55, 145, 0.56);
    -moz-box-shadow: 0px 0px 5px 1px rgba(0, 55, 145, 0.56);
    box-shadow: 0px 0px 5px 1px rgba(0, 55, 145, 0.56);
  }
}

mat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 350px;
  min-width: 300px;
  padding: 20px 10px;
}

mat-card-title {
  margin-bottom: 20px;
}

mat-form-field {
  width: 100%;
}

// Remove o ícone nativo do navegador para mostrar/ocultar senha
.mat-mdc-form-field input[type="password"]::-ms-reveal,
.mat-mdc-form-field input[type="password"]::-ms-clear,
input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear {
  display: none !important;
}

.mat-mdc-form-field input[type="password"]::-webkit-credentials-auto-fill-button,
.mat-mdc-form-field input[type="password"]::-webkit-strong-password-auto-fill-button,
input[type="password"]::-webkit-credentials-auto-fill-button,
input[type="password"]::-webkit-strong-password-auto-fill-button {
  display: none !important;
}

// Remove ícones de senha do Chrome/Edge
.mat-mdc-form-field input[type="password"]::-webkit-textfield-decoration-container {
  display: none !important;
}

// Para Edge específico
.mat-mdc-form-field input[type="password"]::-ms-reveal {
  display: none !important;
}

.form-button {
  width: 100%;
  margin-bottom: 20px;
}

.linhaou {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0px 10px;
  margin-bottom: 15px;
}

.linha {
  width: 100%;
  height: 2px;
  background-color: rgb(196, 196, 196);
}

