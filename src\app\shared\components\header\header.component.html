<div class="header_container">
  <mat-toolbar color="primary" position="start" class="header_toolbar">
    <button mat-icon-button (click)="snav.toggle()">
      <mat-icon>menu</mat-icon>
    </button>
    <span class="spacer"></span>
    <h1 class="app_name">Bora Estudar UFF</h1>
  </mat-toolbar>

  <mat-sidenav-container class="sidenav_container">
    <mat-sidenav
      #snav
      mode="side"
      class="mat_sidenav_content"
    >
      <mat-nav-list>
        @for (nav of fillerNav; track nav) {
        <a mat-list-item routerLink=".">{{ nav }}</a>
        }
      </mat-nav-list>
    </mat-sidenav>
  </mat-sidenav-container>
</div>
