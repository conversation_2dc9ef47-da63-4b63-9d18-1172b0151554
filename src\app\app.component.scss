/* desktop */

.header_toolbar {
  position: fixed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
  height: 72px;
}

.logo img {
  height: 48px;
  /* Tamanho fixo para melhor controle */
  width: auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  /* Permite que os elementos filhos quebrem */
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

.app_name {
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 50%;
  text-align: center;
  position: static;
  transform: none;
}

.user-name-toolbar {
  font-size: 14px;
  color: white;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-name-mobile {
  display: none;
  font-size: 14px;
  color: white;
  font-weight: 500;
}

.header-left button[mat-icon-button] {
  min-width: 36px;
  width: 36px;
  padding: 0;
  margin-right: -8px;
  margin-left: -12px;
}

/* Media Query para Tablet (768px): */
@media (max-width: 768px) {

  .header-left {
      gap: 4px;
      /* Reduz o espaçamento entre elementos */
  }

  .header-left button[mat-icon-button] {
    min-width: 32px;
    width: 32px;
    margin-right: -6px;
  }

  .user-name-toolbar {
    display: none;
  }

  .user-name-mobile {
    display: inline;
  }

  .app_name {
    font-size: 15px;
    /* Tamanho mais legível */
    max-width: 45%;
    /* Espaço extra liberado */
    margin-left: 8px;
    /* Alinhamento refinado */
  }

  .logo img {
    height: 38px;
  }
}

/* Media Query para Mobile (480px):*/
@media (max-width: 480px) {

  /* ADICIONE estas novas regras para o mobile */
  .app_name {
    display: inline-block;
    /* Largura máxima menor para caber no espaço */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .logo img {
    height: 38px;
    /* Reduz um pouco mais a logo */
  }

  .user-name-mobile {
    display: none;
    /* Continua escondendo o nome se necessário */
  }

  .header-left {
    flex: 0 1 auto;
    /* Ocupa apenas o espaço necessário */
  }

  .header-left {
    flex: 0 0 auto;
    /* Não cresce além do necessário */
  }

  .header-left button[mat-icon-button] mat-icon {
    font-size: 18px;
    /* Ícone ligeiramente menor */
  }

  .header-left button[mat-icon-button] {
    margin-left: -12px;
    margin-right: -4px;
  }

  .app_name {
    font-size: 14px;
    max-width: 120px;
    margin: 0 8px;
  }

  .header-right {
    flex: 0 1 auto;
  }
}

/* Ajuste no seu CSS existente */
.sidenav_container {
  margin-top: 18px;
  /* Deve corresponder à altura do seu toolbar */
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

/* Adicione isto */
mat-sidenav-content {
  padding-top: 18px;
  /* Mesmo valor do margin-top */
  height: calc(100vh - 18px);
  overflow-y: auto;
}

/* Se estiver usando algum container principal dentro do sidenav-content */
main {
  padding: 18px;
}
