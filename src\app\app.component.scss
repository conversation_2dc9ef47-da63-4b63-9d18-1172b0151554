.header_toolbar {
  position: fixed;
}

.logo {
  height: 100%;
  img {
    height: 100%;
  }
}

.app_name {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  margin: 0;
  top: 50%;
  transform: translate(-50%, -50%);
  /* Ajuste o top/transform se necessário para alinhar verticalmente */
}

.sidenav_container {
  margin-top: 65px;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.mat_sidenav_content {
  width: 60%;
}

.header_container .sidenav_container {
  flex: 1 0 auto;
}

.example-spacer {
  flex: 1 1 auto;
}

::ng-deep .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{
  width: 100% !important;
}

::ng-deep .p-progress-spinner-circle {
  stroke: #3f51b5 !important;
}

.user-name-toolbar {
  font-size: 14px;
  color: white;
  font-weight: 500;
  padding: 8px 12px;
}

@media (max-width: 768px) {
  .user-name-toolbar {
    display: none;
  }
}
