.container {
  width: 100%;
  height: 100vh;
  background-color: #fafafa;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.logo {
  max-width: 100%;
  padding: 18px;

  img {
    width: 100%;
    box-shadow: 0px 0px 5px 1px rgba(0, 55, 145, 0.56);
    -webkit-box-shadow: 0px 0px 5px 1px rgba(0, 55, 145, 0.56);
    -moz-box-shadow: 0px 0px 5px 1px rgba(0, 55, 145, 0.56);
  }
}

mat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 350px;
  min-width: 300px;
  padding: 20px 10px;
}

mat-card-title {
  margin-bottom: 20px;
}

mat-card-content {
  width: 100%;
  text-align: center;
}

.feedback-message {
  display: flex;
  align-items: center;
  /* alinha ícone e texto na mesma linha vertical */
  justify-content: center;
  gap: 12px;
  /* espaço maior entre ícone e texto */
  margin-bottom: 20px;
  font-size: 1.1rem;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
}
mat-icon[color="warn"] {
  color: #f44336;
  /* vermelho padrão do material */
}

mat-icon[color="primary"] {
  color: #1976d2;
  /* azul padrão do material */
}

mat-icon {
  font-size: 28px;
}

.form-button {
  width: 100%;
  margin-top: 10px;
}
